import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    // Enable server components
    serverComponentsExternalPackages: ["typeorm", "oracledb"],
  },
  webpack: (config, { isServer }) => {
    // Handle TypeORM and Oracle DB modules
    if (isServer) {
      config.externals.push("oracledb");
    }

    // Handle reflect-metadata for TypeORM decorators
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },
  // Enable TypeScript strict mode
  typescript: {
    ignoreBuildErrors: false,
  },
};

export default nextConfig;
