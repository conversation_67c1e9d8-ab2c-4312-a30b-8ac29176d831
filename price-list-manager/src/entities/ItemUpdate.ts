import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { User } from "./User";
import { Item } from "./Item";

@Entity("item_updates")
export class ItemUpdate {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100 })
  fieldName: string; // Name of the field that was updated

  @Column({ type: "text", nullable: true })
  oldValue?: string; // Previous value (JSON string for complex objects)

  @Column({ type: "text", nullable: true })
  newValue?: string; // New value (JSON string for complex objects)

  @Column({ type: "varchar", length: 500, nullable: true })
  reason?: string; // Reason for the update

  @CreateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.updatedItems)
  @JoinColumn({ name: "updated_by" })
  updatedBy: User;

  @Column({ type: "varchar", length: 36 })
  updatedById: string;

  @ManyToOne(() => Item, (item) => item.updates)
  @JoinColumn({ name: "item_id" })
  item: Item;

  @Column({ type: "varchar", length: 36 })
  itemId: string;
}
