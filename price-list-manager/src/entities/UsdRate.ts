import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Nature } from "./Nature";
import { Status } from "./Status";

@Entity("usd_rates")
export class UsdRate {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "decimal", precision: 15, scale: 4 })
  rate: number; // USD to LBP rate

  @Column({ type: "date" })
  effectiveDate: Date; // Date when this rate becomes effective

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Nature, (nature) => nature.usdRates)
  @JoinColumn({ name: "nature_id" })
  nature: Nature;

  @Column({ type: "varchar", length: 36 })
  natureId: string;

  @ManyToOne(() => Status, (status) => status.usdRates)
  @JoinColumn({ name: "status_id" })
  status: Status;

  @Column({ type: "varchar", length: 36 })
  statusId: string;
}
