import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Item } from "./Item";
import { ItemUpdate } from "./ItemUpdate";
import { Coefficient } from "./Coefficient";

export enum UserRole {
  ADMIN = "ADMIN",
  PLANNING_PURCHASING_OFFICER = "PLANNING_PURCHASING_OFFICER",
  SALES_COORDINATOR = "SALES_COORDINATOR",
  FINANCE_MANAGER = "FINANCE_MANAGER",
  USER = "USER",
}

@Entity("users")
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100, unique: true })
  username: string;

  @Column({ type: "varchar", length: 255, unique: true })
  email: string;

  @Column({ type: "varchar", length: 255 })
  password: string;

  @Column({
    type: "varchar",
    length: 50,
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Oracle EBS integration fields
  @Column({ type: "varchar", length: 100, nullable: true })
  oracleUserId?: string;

  @Column({ type: "varchar", length: 1000, nullable: true })
  oracleToken?: string;

  // Relations - Audit trail
  @OneToMany(() => Item, (item) => item.createdBy)
  createdItems: Item[];

  @OneToMany(() => ItemUpdate, (itemUpdate) => itemUpdate.updatedBy)
  updatedItems: ItemUpdate[];

  @OneToMany(() => Coefficient, (coefficient) => coefficient.createdBy)
  createdCoefficients: Coefficient[];
}
