import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Item } from "./Item";
import { PriceStructure } from "./PriceStructure";
import { ItemClassificationRange } from "./ItemClassificationRange";

@Entity("item_classifications")
export class ItemClassification {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 50, unique: true })
  code: string; // A, B, C, D, E, F, G, H, I, J

  @Column({ type: "varchar", length: 255 })
  description: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => Item, (item) => item.itemClassification)
  items: Item[];

  @OneToMany(() => PriceStructure, (priceStructure) => priceStructure.itemClassification)
  priceStructures: PriceStructure[];

  @OneToMany(() => ItemClassificationRange, (range) => range.itemClassification)
  itemClassificationRanges: ItemClassificationRange[];
}
