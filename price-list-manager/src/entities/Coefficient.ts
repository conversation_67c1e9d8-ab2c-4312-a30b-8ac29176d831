import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { User } from "./User";

@Entity("coefficients")
export class Coefficient {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100, unique: true })
  name: string; // e.g., "Freight", "Insurance", "Handling", etc.

  @Column({ type: "decimal", precision: 15, scale: 4 })
  value: number; // Coefficient value

  @Column({ type: "varchar", length: 500, nullable: true })
  description?: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations - Audit trail
  @ManyToOne(() => User, (user) => user.createdCoefficients)
  @JoinColumn({ name: "created_by" })
  createdBy: User;

  @Column({ type: "varchar", length: 36 })
  createdById: string;
}
