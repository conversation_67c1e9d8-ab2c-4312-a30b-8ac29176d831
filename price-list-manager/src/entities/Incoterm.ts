import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Item } from "./Item";
import { PriceStructure } from "./PriceStructure";
import { ItemClassificationRange } from "./ItemClassificationRange";

@Entity("incoterms")
export class Incoterm {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 10, unique: true })
  code: string; // FOB, CIP, LOC, etc.

  @Column({ type: "varchar", length: 255 })
  description: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => Item, (item) => item.incoterm)
  items: Item[];

  @OneToMany(() => PriceStructure, (priceStructure) => priceStructure.incoterm)
  priceStructures: PriceStructure[];

  @OneToMany(() => ItemClassificationRange, (range) => range.incoterm)
  itemClassificationRanges: ItemClassificationRange[];
}
