import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Item } from "./Item";
import { PriceStructure } from "./PriceStructure";
import { UsdRate } from "./UsdRate";

@Entity("statuses")
export class Status {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 30, unique: true })
  code: string; // Subsidized, Partially Subsidized, Non Subsidized

  @Column({ type: "varchar", length: 255 })
  description: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => Item, (item) => item.status)
  items: Item[];

  @OneToMany(() => PriceStructure, (priceStructure) => priceStructure.status)
  priceStructures: PriceStructure[];

  @OneToMany(() => UsdRate, (usdRate) => usdRate.status)
  usdRates: UsdRate[];
}
