import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { PriceList } from "./PriceList";
import { Item } from "./Item";

@Entity("price_list_items")
export class PriceListItem {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "decimal", precision: 15, scale: 4 })
  unitPrice: number; // Final price for this item in this price list

  @Column({ type: "decimal", precision: 5, scale: 2, nullable: true })
  discountPercentage?: number; // Optional discount percentage

  @Column({ type: "decimal", precision: 15, scale: 4, nullable: true })
  minimumQuantity?: number; // Minimum quantity for this price

  @Column({ type: "decimal", precision: 15, scale: 4, nullable: true })
  maximumQuantity?: number; // Maximum quantity for this price

  @Column({ type: "varchar", length: 500, nullable: true })
  notes?: string; // Additional notes for this price list item

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => PriceList, (priceList) => priceList.priceListItems)
  @JoinColumn({ name: "price_list_id" })
  priceList: PriceList;

  @Column({ type: "varchar", length: 36 })
  priceListId: string;

  @ManyToOne(() => Item, (item) => item.priceListItems)
  @JoinColumn({ name: "item_id" })
  item: Item;

  @Column({ type: "varchar", length: 36 })
  itemId: string;
}
