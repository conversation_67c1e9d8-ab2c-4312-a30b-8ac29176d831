import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("currency_rates")
export class CurrencyRate {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 3 })
  fromCurrency: string; // e.g., "USD"

  @Column({ type: "varchar", length: 3 })
  toCurrency: string; // e.g., "LBP"

  @Column({ type: "decimal", precision: 15, scale: 4 })
  rate: number; // Exchange rate

  @Column({ type: "date" })
  effectiveDate: Date; // Date when this rate becomes effective

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
