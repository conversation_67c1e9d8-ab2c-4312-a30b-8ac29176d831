import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Incoterm } from "./Incoterm";
import { Nature } from "./Nature";
import { Status } from "./Status";
import { ItemClassification } from "./ItemClassification";

@Entity("price_structures")
export class PriceStructure {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "decimal", precision: 5, scale: 2 })
  percentage: number; // Percentage value (e.g., 15.50 for 15.5%)

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Incoterm, (incoterm) => incoterm.priceStructures)
  @JoinColumn({ name: "incoterm_id" })
  incoterm: Incoterm;

  @Column({ type: "varchar", length: 36 })
  incotermId: string;

  @ManyToOne(() => Nature, (nature) => nature.priceStructures)
  @JoinColumn({ name: "nature_id" })
  nature: Nature;

  @Column({ type: "varchar", length: 36 })
  natureId: string;

  @ManyToOne(() => Status, (status) => status.priceStructures)
  @JoinColumn({ name: "status_id" })
  status: Status;

  @Column({ type: "varchar", length: 36 })
  statusId: string;

  @ManyToOne(() => ItemClassification, (itemClassification) => itemClassification.priceStructures)
  @JoinColumn({ name: "item_classification_id" })
  itemClassification: ItemClassification;

  @Column({ type: "varchar", length: 36 })
  itemClassificationId: string;
}
