import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { Item } from "./Item";
import { PriceStructure } from "./PriceStructure";
import { UsdRate } from "./UsdRate";

@Entity("natures")
export class Nature {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 20, unique: true })
  code: string; // Imported, Local, etc.

  @Column({ type: "varchar", length: 255 })
  description: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => Item, (item) => item.nature)
  items: Item[];

  @OneToMany(() => PriceStructure, (priceStructure) => priceStructure.nature)
  priceStructures: PriceStructure[];

  @OneToMany(() => UsdRate, (usdRate) => usdRate.nature)
  usdRates: UsdRate[];
}
