import {
  <PERSON><PERSON>ty,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { ItemClassification } from "./ItemClassification";
import { Incoterm } from "./Incoterm";

@Entity("item_classification_ranges")
export class ItemClassificationRange {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "decimal", precision: 15, scale: 4 })
  minValue: number;

  @Column({ type: "decimal", precision: 15, scale: 4 })
  maxValue: number;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => ItemClassification, (itemClassification) => itemClassification.itemClassificationRanges)
  @JoinColumn({ name: "item_classification_id" })
  itemClassification: ItemClassification;

  @Column({ type: "varchar", length: 36 })
  itemClassificationId: string;

  @ManyToOne(() => Incoterm, (incoterm) => incoterm.itemClassificationRanges)
  @JoinColumn({ name: "incoterm_id" })
  incoterm: Incoterm;

  @Column({ type: "varchar", length: 36 })
  incotermId: string;
}
