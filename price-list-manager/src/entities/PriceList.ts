import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";
import { PriceListItem } from "./PriceListItem";

export enum PriceListStatus {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  ARCHIVED = "ARCHIVED",
}

@Entity("price_lists")
export class PriceList {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 100, unique: true })
  name: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  description?: string;

  @Column({
    type: "varchar",
    length: 20,
    enum: PriceListStatus,
    default: PriceListStatus.DRAFT,
  })
  status: PriceListStatus;

  @Column({ type: "date" })
  effectiveDate: Date;

  @Column({ type: "date", nullable: true })
  expirationDate?: Date;

  @Column({ type: "varchar", length: 3, default: "LBP" })
  currency: string; // Currency for this price list

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => PriceListItem, (priceListItem) => priceListItem.priceList)
  priceListItems: PriceListItem[];
}
