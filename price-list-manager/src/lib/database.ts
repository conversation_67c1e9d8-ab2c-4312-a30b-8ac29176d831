import { AppDataSource, initializeDatabase } from "../data-source/AppDataSource";
import { User } from "../entities/User";
import { Incoterm } from "../entities/Incoterm";
import { Nature } from "../entities/Nature";
import { Status } from "../entities/Status";
import { ItemClassification } from "../entities/ItemClassification";
import { ItemClassificationRange } from "../entities/ItemClassificationRange";
import { PriceStructure } from "../entities/PriceStructure";
import { Coefficient } from "../entities/Coefficient";
import { CurrencyRate } from "../entities/CurrencyRate";
import { UsdRate } from "../entities/UsdRate";
import { Item } from "../entities/Item";
import { ItemUpdate } from "../entities/ItemUpdate";
import { PriceList } from "../entities/PriceList";
import { PriceListItem } from "../entities/PriceListItem";

// Database service class that provides repository access
export class DatabaseService {
  private static instance: DatabaseService;

  private constructor() {}

  public static async getInstance(): Promise<DatabaseService> {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
      await initializeDatabase();
    }
    return DatabaseService.instance;
  }

  // Repository getters
  get userRepository() {
    return AppDataSource.getRepository(User);
  }

  get incotermRepository() {
    return AppDataSource.getRepository(Incoterm);
  }

  get natureRepository() {
    return AppDataSource.getRepository(Nature);
  }

  get statusRepository() {
    return AppDataSource.getRepository(Status);
  }

  get itemClassificationRepository() {
    return AppDataSource.getRepository(ItemClassification);
  }

  get itemClassificationRangeRepository() {
    return AppDataSource.getRepository(ItemClassificationRange);
  }

  get priceStructureRepository() {
    return AppDataSource.getRepository(PriceStructure);
  }

  get coefficientRepository() {
    return AppDataSource.getRepository(Coefficient);
  }

  get currencyRateRepository() {
    return AppDataSource.getRepository(CurrencyRate);
  }

  get usdRateRepository() {
    return AppDataSource.getRepository(UsdRate);
  }

  get itemRepository() {
    return AppDataSource.getRepository(Item);
  }

  get itemUpdateRepository() {
    return AppDataSource.getRepository(ItemUpdate);
  }

  get priceListRepository() {
    return AppDataSource.getRepository(PriceList);
  }

  get priceListItemRepository() {
    return AppDataSource.getRepository(PriceListItem);
  }

  // Utility methods
  async isConnected(): Promise<boolean> {
    return AppDataSource.isInitialized;
  }

  async disconnect(): Promise<void> {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Export a singleton instance
export const db = DatabaseService.getInstance();
