import "reflect-metadata";
import { AppDataSource } from "../data-source/AppDataSource";

let isInitialized = false;

export async function initializeTypeORM() {
  if (!isInitialized && !AppDataSource.isInitialized) {
    try {
      await AppDataSource.initialize();
      isInitialized = true;
      console.log("TypeORM initialized successfully");
    } catch (error) {
      console.error("Error initializing TypeORM:", error);
      throw error;
    }
  }
  return AppDataSource;
}

export { AppDataSource };
