import "reflect-metadata";
import { DataSource } from "typeorm";
import { User } from "../entities/User";
import { Incoterm } from "../entities/Incoterm";
import { Nature } from "../entities/Nature";
import { Status } from "../entities/Status";
import { ItemClassification } from "../entities/ItemClassification";
import { ItemClassificationRange } from "../entities/ItemClassificationRange";
import { PriceStructure } from "../entities/PriceStructure";
import { Coefficient } from "../entities/Coefficient";
import { CurrencyRate } from "../entities/CurrencyRate";
import { UsdRate } from "../entities/UsdRate";
import { Item } from "../entities/Item";
import { ItemUpdate } from "../entities/ItemUpdate";
import { PriceList } from "../entities/PriceList";
import { PriceListItem } from "../entities/PriceListItem";

export const AppDataSource = new DataSource({
  type: "oracle",
  host: process.env.ORACLE_HOST || "localhost",
  port: parseInt(process.env.ORACLE_PORT || "1521"),
  username: process.env.ORACLE_USERNAME || "price_list_user",
  password: process.env.ORACLE_PASSWORD || "your_password",
  serviceName: process.env.ORACLE_SERVICE_NAME || "XE",
  schema: process.env.ORACLE_SCHEMA || "PRICE_LIST_SCHEMA",
  synchronize: process.env.NODE_ENV === "development",
  logging: process.env.NODE_ENV === "development",
  entities: [
    User,
    Incoterm,
    Nature,
    Status,
    ItemClassification,
    ItemClassificationRange,
    PriceStructure,
    Coefficient,
    CurrencyRate,
    UsdRate,
    Item,
    ItemUpdate,
    PriceList,
    PriceListItem,
  ],
  migrations: ["src/migrations/*.ts"],
  subscribers: ["src/subscribers/*.ts"],
  extra: {
    // Oracle connection pool settings
    poolMin: parseInt(process.env.ORACLE_POOL_MIN || "5"),
    poolMax: parseInt(process.env.ORACLE_POOL_MAX || "20"),
    poolTimeout: parseInt(process.env.ORACLE_POOL_TIMEOUT || "30000"),
    // Oracle-specific settings
    connectString: process.env.DATABASE_URL || 
      `${process.env.ORACLE_HOST || "localhost"}:${process.env.ORACLE_PORT || "1521"}/${process.env.ORACLE_SERVICE_NAME || "XE"}`,
  },
});

// Initialize the data source
export const initializeDatabase = async () => {
  try {
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log("Oracle Database connection established successfully");
    }
  } catch (error) {
    console.error("Error during Oracle Database initialization:", error);
    throw error;
  }
};

// Close the data source
export const closeDatabase = async () => {
  try {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("Oracle Database connection closed successfully");
    }
  } catch (error) {
    console.error("Error during Oracle Database closure:", error);
    throw error;
  }
};
