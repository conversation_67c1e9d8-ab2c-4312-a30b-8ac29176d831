import "reflect-metadata";
import { AppDataSource } from "../data-source/AppDataSource";
import { User, UserRole } from "../entities/User";
import { Incoterm } from "../entities/Incoterm";
import { Nature } from "../entities/Nature";
import { Status } from "../entities/Status";
import { ItemClassification } from "../entities/ItemClassification";
import { ItemClassificationRange } from "../entities/ItemClassificationRange";
import { PriceStructure } from "../entities/PriceStructure";
import { Coefficient } from "../entities/Coefficient";
import { CurrencyRate } from "../entities/CurrencyRate";
import { UsdRate } from "../entities/UsdRate";
import bcrypt from "bcryptjs";

async function seed() {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Get repositories
    const userRepo = AppDataSource.getRepository(User);
    const incotermRepo = AppDataSource.getRepository(Incoterm);
    const natureRepo = AppDataSource.getRepository(Nature);
    const statusRepo = AppDataSource.getRepository(Status);
    const itemClassificationRepo = AppDataSource.getRepository(ItemClassification);
    const itemClassificationRangeRepo = AppDataSource.getRepository(ItemClassificationRange);
    const priceStructureRepo = AppDataSource.getRepository(PriceStructure);
    const coefficientRepo = AppDataSource.getRepository(Coefficient);
    const currencyRateRepo = AppDataSource.getRepository(CurrencyRate);
    const usdRateRepo = AppDataSource.getRepository(UsdRate);

    // Clear existing data (in development only)
    if (process.env.NODE_ENV === "development") {
      console.log("Clearing existing data...");
      await usdRateRepo.delete({});
      await currencyRateRepo.delete({});
      await coefficientRepo.delete({});
      await priceStructureRepo.delete({});
      await itemClassificationRangeRepo.delete({});
      await itemClassificationRepo.delete({});
      await statusRepo.delete({});
      await natureRepo.delete({});
      await incotermRepo.delete({});
      await userRepo.delete({});
    }

    // Create admin user
    console.log("Creating admin user...");
    const hashedPassword = await bcrypt.hash("admin123", 10);
    const adminUser = userRepo.create({
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      role: UserRole.ADMIN,
      isActive: true,
    });
    await userRepo.save(adminUser);

    // Create test users
    console.log("Creating test users...");
    const testUsers = [
      {
        username: "planning_officer",
        email: "<EMAIL>",
        role: UserRole.PLANNING_PURCHASING_OFFICER,
      },
      {
        username: "sales_coordinator",
        email: "<EMAIL>",
        role: UserRole.SALES_COORDINATOR,
      },
      {
        username: "finance_manager",
        email: "<EMAIL>",
        role: UserRole.FINANCE_MANAGER,
      },
    ];

    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash("password123", 10);
      const user = userRepo.create({
        ...userData,
        password: hashedPassword,
        isActive: true,
      });
      await userRepo.save(user);
    }

    // Create Incoterms
    console.log("Creating Incoterms...");
    const incoterms = [
      { code: "FOB", description: "Free On Board" },
      { code: "CIP", description: "Carriage and Insurance Paid To" },
      { code: "LOC", description: "Local" },
    ];

    const savedIncoterms = [];
    for (const incotermData of incoterms) {
      const incoterm = incotermRepo.create(incotermData);
      const saved = await incotermRepo.save(incoterm);
      savedIncoterms.push(saved);
    }

    // Create Natures
    console.log("Creating Natures...");
    const natures = [
      { code: "Imported", description: "Imported goods" },
      { code: "Local", description: "Locally sourced goods" },
    ];

    const savedNatures = [];
    for (const natureData of natures) {
      const nature = natureRepo.create(natureData);
      const saved = await natureRepo.save(nature);
      savedNatures.push(saved);
    }

    // Create Statuses
    console.log("Creating Statuses...");
    const statuses = [
      { code: "Subsidized", description: "Subsidized items" },
      { code: "Partially Subsidized", description: "Partially subsidized items" },
      { code: "Non Subsidized", description: "Non-subsidized items" },
    ];

    const savedStatuses = [];
    for (const statusData of statuses) {
      const status = statusRepo.create(statusData);
      const saved = await statusRepo.save(status);
      savedStatuses.push(saved);
    }

    // Create Item Classifications
    console.log("Creating Item Classifications...");
    const classifications = [
      { code: "A", description: "Classification A" },
      { code: "B", description: "Classification B" },
      { code: "C", description: "Classification C" },
      { code: "D", description: "Classification D" },
      { code: "E", description: "Classification E" },
      { code: "F", description: "Classification F" },
      { code: "G", description: "Classification G" },
      { code: "H", description: "Classification H" },
      { code: "I", description: "Classification I" },
      { code: "J", description: "Classification J" },
    ];

    const savedClassifications = [];
    for (const classificationData of classifications) {
      const classification = itemClassificationRepo.create(classificationData);
      const saved = await itemClassificationRepo.save(classification);
      savedClassifications.push(saved);
    }

    console.log("Seed data created successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
    throw error;
  } finally {
    // Close the database connection
    await AppDataSource.destroy();
  }
}

// Run the seed function
seed().catch((error) => {
  console.error("Seed failed:", error);
  process.exit(1);
});
