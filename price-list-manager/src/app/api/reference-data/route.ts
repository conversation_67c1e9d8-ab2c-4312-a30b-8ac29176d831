import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';
import { ApiResponse } from '@/types';

// GET /api/reference-data - Get all reference data for dropdowns and forms
export async function GET(request: NextRequest) {
  try {
    const db = await DatabaseService.getInstance();
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type) {
      // Get specific reference data type
      let data;
      switch (type) {
        case 'incoterms':
          data = await db.incotermRepository.find({
            where: { isActive: true },
            order: { code: 'ASC' },
          });
          break;
        case 'natures':
          data = await db.natureRepository.find({
            where: { isActive: true },
            order: { code: 'ASC' },
          });
          break;
        case 'statuses':
          data = await db.statusRepository.find({
            where: { isActive: true },
            order: { code: 'ASC' },
          });
          break;
        case 'item-classifications':
          data = await db.itemClassificationRepository.find({
            where: { isActive: true },
            order: { code: 'ASC' },
          });
          break;
        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid reference data type',
            } as ApiResponse<never>,
            { status: 400 }
          );
      }

      return NextResponse.json({
        success: true,
        data,
      } as ApiResponse<any>);
    }

    // Get all reference data
    const [incoterms, natures, statuses, itemClassifications] = await Promise.all([
      db.incotermRepository.find({
        where: { isActive: true },
        order: { code: 'ASC' },
      }),
      db.natureRepository.find({
        where: { isActive: true },
        order: { code: 'ASC' },
      }),
      db.statusRepository.find({
        where: { isActive: true },
        order: { code: 'ASC' },
      }),
      db.itemClassificationRepository.find({
        where: { isActive: true },
        order: { code: 'ASC' },
      }),
    ]);

    const referenceData = {
      incoterms,
      natures,
      statuses,
      itemClassifications,
    };

    return NextResponse.json({
      success: true,
      data: referenceData,
    } as ApiResponse<typeof referenceData>);
  } catch (error) {
    console.error('Error fetching reference data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch reference data',
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}

// POST /api/reference-data - Create new reference data entry
export async function POST(request: NextRequest) {
  try {
    const db = await DatabaseService.getInstance();
    const body = await request.json();
    const { type, code, description } = body;

    if (!type || !code || !description) {
      return NextResponse.json(
        {
          success: false,
          error: 'Type, code, and description are required',
        } as ApiResponse<never>,
        { status: 400 }
      );
    }

    let data;
    switch (type) {
      case 'incoterm':
        // Check if code already exists
        const existingIncoterm = await db.incotermRepository.findOne({
          where: { code },
        });
        if (existingIncoterm) {
          return NextResponse.json(
            {
              success: false,
              error: 'Incoterm code already exists',
            } as ApiResponse<never>,
            { status: 409 }
          );
        }
        data = await db.incotermRepository.save(
          db.incotermRepository.create({ code, description })
        );
        break;
      case 'nature':
        const existingNature = await db.natureRepository.findOne({
          where: { code },
        });
        if (existingNature) {
          return NextResponse.json(
            {
              success: false,
              error: 'Nature code already exists',
            } as ApiResponse<never>,
            { status: 409 }
          );
        }
        data = await db.natureRepository.save(
          db.natureRepository.create({ code, description })
        );
        break;
      case 'status':
        const existingStatus = await db.statusRepository.findOne({
          where: { code },
        });
        if (existingStatus) {
          return NextResponse.json(
            {
              success: false,
              error: 'Status code already exists',
            } as ApiResponse<never>,
            { status: 409 }
          );
        }
        data = await db.statusRepository.save(
          db.statusRepository.create({ code, description })
        );
        break;
      case 'item-classification':
        const existingClassification = await db.itemClassificationRepository.findOne({
          where: { code },
        });
        if (existingClassification) {
          return NextResponse.json(
            {
              success: false,
              error: 'Item classification code already exists',
            } as ApiResponse<never>,
            { status: 409 }
          );
        }
        data = await db.itemClassificationRepository.save(
          db.itemClassificationRepository.create({ code, description })
        );
        break;
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid reference data type',
          } as ApiResponse<never>,
          { status: 400 }
        );
    }

    return NextResponse.json(
      {
        success: true,
        data,
        message: `${type} created successfully`,
      } as ApiResponse<any>,
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating reference data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create reference data',
      } as ApiResponse<never>,
      { status: 500 }
    );
  }
}
