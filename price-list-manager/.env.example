# Environment variables declared in this file are automatically made available to <PERSON>risma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Oracle Database Configuration
# Format: oracle://username:password@hostname:port/service_name
# Development (Oracle XE)
DATABASE_URL="oracle://price_list_user:your_password@localhost:1521/XE"

# Production (Oracle Cloud or Enterprise)
# DATABASE_URL="oracle://username:password@hostname:1521/service_name"

# Oracle Connection Pool Settings
ORACLE_POOL_MIN=5
ORACLE_POOL_MAX=20
ORACLE_POOL_TIMEOUT=30000

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Oracle EBS Integration
ORACLE_EBS_API_URL="https://your-oracle-ebs-api.com"
ORACLE_EBS_CLIENT_ID="your-client-id"
ORACLE_EBS_CLIENT_SECRET="your-client-secret"

# JWT
JWT_SECRET="your-jwt-secret-here-change-in-production"

# Application
NODE_ENV="development"

# Oracle Database Schema (for production)
ORACLE_SCHEMA="PRICE_LIST_SCHEMA"

# Oracle Tablespace (for production)
ORACLE_TABLESPACE="PRICE_LIST_DATA"
ORACLE_INDEX_TABLESPACE="PRICE_LIST_INDEX"
