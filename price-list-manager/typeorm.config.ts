import { DataSource } from "typeorm";
import { config } from "dotenv";

// Load environment variables
config();

export default new DataSource({
  type: "oracle",
  host: process.env.ORACLE_HOST || "localhost",
  port: parseInt(process.env.ORACLE_PORT || "1521"),
  username: process.env.ORACLE_USERNAME || "price_list_user",
  password: process.env.ORACLE_PASSWORD || "your_password",
  serviceName: process.env.ORACLE_SERVICE_NAME || "XE",
  schema: process.env.ORACLE_SCHEMA || "PRICE_LIST_SCHEMA",
  synchronize: process.env.NODE_ENV === "development",
  logging: process.env.NODE_ENV === "development",
  entities: ["src/entities/*.ts"],
  migrations: ["src/migrations/*.ts"],
  subscribers: ["src/subscribers/*.ts"],
  extra: {
    // Oracle connection pool settings
    poolMin: parseInt(process.env.ORACLE_POOL_MIN || "5"),
    poolMax: parseInt(process.env.ORACLE_POOL_MAX || "20"),
    poolTimeout: parseInt(process.env.ORACLE_POOL_TIMEOUT || "30000"),
  },
});
