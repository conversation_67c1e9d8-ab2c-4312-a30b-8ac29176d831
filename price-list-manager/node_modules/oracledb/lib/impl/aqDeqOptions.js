// Copyright (c) 2022, Oracle and/or its affiliates.

//-----------------------------------------------------------------------------
//
// This software is dual-licensed to you under the Universal Permissive License
// (UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
// 2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
// either license.
//
// If you elect to accept the software under the Apache License, Version 2.0,
// the following applies:
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//-----------------------------------------------------------------------------

'use strict';

const errors = require('../errors.js');

class AqDeqOptionsImpl {

  //---------------------------------------------------------------------------
  // getCondition()
  //
  // Returns the condition to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getCondition() {
    errors.throwNotImplemented("getting condition (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getConsumerName()
  //
  // Returns the consumer name to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getConsumerName() {
    errors.throwNotImplemented("getting consumer name (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getCorrelation()
  //
  // Returns the correlation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getCorrelation() {
    errors.throwNotImplemented("getting correlation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getMode()
  //
  // Returns the mode to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getMode() {
    errors.throwNotImplemented("getting mode (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getMsgId()
  //
  // Returns the message id to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getMsgId() {
    errors.throwNotImplemented("getting message id (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getNavigation()
  //
  // Returns the navigation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getNavigation() {
    errors.throwNotImplemented("getting navigation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getTransformation()
  //
  // Returns the transformation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getTransformation() {
    errors.throwNotImplemented("getting transformation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getVisibility()
  //
  // Returns the visibility to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getVisibility() {
    errors.throwNotImplemented("getting visibility (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // getWait()
  //
  // Returns the wait to use for dequeuing messages.
  //---------------------------------------------------------------------------
  getWait() {
    errors.throwNotImplemented("getting wait (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setCondition()
  //
  // Sets the condition to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setCondition() {
    errors.throwNotImplemented("setting condition (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setConsumerName()
  //
  // Sets the consumer name to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setConsumerName() {
    errors.throwNotImplemented("setting consumer name (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setCorrelation()
  //
  // Sets the correlation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setCorrelation() {
    errors.throwNotImplemented("setting correlation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setMode()
  //
  // Sets the mode to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setMode() {
    errors.throwNotImplemented("setting mode (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setMsgId()
  //
  // Sets the message id to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setMsgId() {
    errors.throwNotImplemented("setting message id (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setNavigation()
  //
  // Sets the navigation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setNavigation() {
    errors.throwNotImplemented("setting navigation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setTransformation()
  //
  // Sets the transformation to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setTransformation() {
    errors.throwNotImplemented("setting transformation (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setVisibility()
  //
  // Sets the visibility to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setVisibility() {
    errors.throwNotImplemented("setting visibility (dequeue options)");
  }

  //---------------------------------------------------------------------------
  // setWait()
  //
  // Sets the wait to use for dequeuing messages.
  //---------------------------------------------------------------------------
  setWait() {
    errors.throwNotImplemented("setting wait (dequeue options)");
  }

}

module.exports = AqDeqOptionsImpl;
