// Copyright (c) 2019, 2022, Oracle and/or its affiliates.

//-----------------------------------------------------------------------------
//
// This software is dual-licensed to you under the Universal Permissive License
// (UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
// 2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
// either license.
//
// If you elect to accept the software under the Apache License, Version 2.0,
// the following applies:
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//-----------------------------------------------------------------------------

'use strict';

const errors = require('../errors.js');

class AqMessageImpl {

  //---------------------------------------------------------------------------
  // getCorrelation()
  //
  // Returns the correlation associated with the message.
  //---------------------------------------------------------------------------
  getCorrelation() {
    errors.throwNotImplemented("getting correlation (message)");
  }

  //---------------------------------------------------------------------------
  // getDelay()
  //
  // Returns the delay associated with the message.
  //---------------------------------------------------------------------------
  getDelay() {
    errors.throwNotImplemented("getting delay (message)");
  }

  //---------------------------------------------------------------------------
  // getDeliveryMode()
  //
  // Returns the delivery mode associated with the message.
  //---------------------------------------------------------------------------
  getDeliveryMode() {
    errors.throwNotImplemented("getting delivery mode (message)");
  }

  //---------------------------------------------------------------------------
  // getExceptionQueue()
  //
  // Returns the exception queue associated with the message.
  //---------------------------------------------------------------------------
  getExceptionQueue() {
    errors.throwNotImplemented("getting exception queue (message)");
  }

  //---------------------------------------------------------------------------
  // getExpiration()
  //
  // Returns the expiration associated with the message.
  //---------------------------------------------------------------------------
  getExpiration() {
    errors.throwNotImplemented("getting expiration (message)");
  }

  //---------------------------------------------------------------------------
  // getMsgId()
  //
  // Returns the message id associated with the message.
  //---------------------------------------------------------------------------
  getMsgId() {
    errors.throwNotImplemented("getting message id (message)");
  }

  //---------------------------------------------------------------------------
  // getNumAttempts()
  //
  // Returns the number of attempts associated with the message.
  //---------------------------------------------------------------------------
  getNumAttempts() {
    errors.throwNotImplemented("getting number of attempts (message)");
  }

  //---------------------------------------------------------------------------
  // getOriginalMsgId()
  //
  // Returns the original message id associated with the message.
  //---------------------------------------------------------------------------
  getOriginalMsgId() {
    errors.throwNotImplemented("getting original message id (message)");
  }

  //---------------------------------------------------------------------------
  // getPayload()
  //
  // Returns the payload associated with the message.
  //---------------------------------------------------------------------------
  getPayload() {
    errors.throwNotImplemented("getting payload (message)");
  }

  //---------------------------------------------------------------------------
  // getPriority()
  //
  // Returns the priority associated with the message.
  //---------------------------------------------------------------------------
  getPriority() {
    errors.throwNotImplemented("getting priority (message)");
  }

  //---------------------------------------------------------------------------
  // getState()
  //
  // Returns the state associated with the message.
  //---------------------------------------------------------------------------
  getState() {
    errors.throwNotImplemented("getting state (message)");
  }

}

module.exports = AqMessageImpl;
