// Copyright (c) 2022, 2023, Oracle and/or its affiliates.

//-----------------------------------------------------------------------------
//
// This software is dual-licensed to you under the Universal Permissive License
// (UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
// 2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
// either license.
//
// If you elect to accept the software under the Apache License, Version 2.0,
// the following applies:
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//-----------------------------------------------------------------------------

'use strict';

const errors = require('../errors.js');

class SodaDocumentImpl {

  //---------------------------------------------------------------------------
  // getContentAsBuffer()
  //
  // Returns the document content as a Buffer object.
  //---------------------------------------------------------------------------
  getContentAsBuffer() {
    errors.throwNotImplemented("getting doc content as a buffer");
  }

  //---------------------------------------------------------------------------
  // getContentAsString()
  //
  // Returns the document content as a string.
  //---------------------------------------------------------------------------
  getContentAsString() {
    errors.throwNotImplemented("getting doc content as a string");
  }

  //---------------------------------------------------------------------------
  // getCreatedOn()
  //
  // Returns the date the document was created.
  //---------------------------------------------------------------------------
  getCreatedOn() {
    errors.throwNotImplemented("getting the created date of a document");
  }

  //---------------------------------------------------------------------------
  // getKey()
  //
  // Returns the key of the document.
  //---------------------------------------------------------------------------
  getKey() {
    errors.throwNotImplemented("getting the key of a document");
  }

  //---------------------------------------------------------------------------
  // getLastModified()
  //
  // Returns the date the document was last modified.
  //---------------------------------------------------------------------------
  getLastModified() {
    errors.throwNotImplemented("getting the last modified date of a doc");
  }

  //---------------------------------------------------------------------------
  // getMediaType()
  //
  // Returns the media type of the document.
  //---------------------------------------------------------------------------
  getMediaType() {
    errors.throwNotImplemented("getting the media type of a document");
  }

  //---------------------------------------------------------------------------
  // getVersion()
  //
  // Returns the version of the document.
  //---------------------------------------------------------------------------
  getVersion() {
    errors.throwNotImplemented("getting the version of a document");
  }

}

module.exports = SodaDocumentImpl;
