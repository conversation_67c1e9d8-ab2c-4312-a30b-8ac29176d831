{"name": "@types/oracledb", "version": "6.6.1", "description": "TypeScript definitions for oracledb", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/oracledb", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/connorjayfitzgerald"}, {"name": "<PERSON>", "githubUsername": "dannyb648", "url": "https://github.com/dannyb648"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jacobwheale"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/oracledb"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "06f482ea108538f371c875966d90596eff974a202953f9010389b8bcf839be0e", "typeScriptVersion": "5.1"}