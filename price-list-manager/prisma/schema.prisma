// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "oracle"
  url      = env("DATABASE_URL")
}

// User Management and Authentication
model User {
  id          String   @id @default(cuid()) @db.VarChar(30)
  username    String   @unique @db.VarChar(100)
  email       String   @unique @db.VarChar(255)
  password    String   @db.VarChar(255)
  role        UserRole @default(USER)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Oracle EBS integration fields
  oracleUserId String? @db.VarChar(100)
  oracleToken  String? @db.Var<PERSON>har(1000)

  // Audit trail
  createdItems     Item[]
  updatedItems     ItemUpdate[]
  createdCoefficients Coefficient[]

  @@map("users")
}

enum UserRole {
  ADMIN
  PLANNING_PURCHASING_OFFICER
  SALES_COORDINATOR
  SENIOR_REGULATORY_SPECIALIST
  GROUP_REGULATORY_AFFAIRS_MANAGER
  USER
}

// 1. Incoterms List
model Incoterm {
  id          String @id @default(cuid()) @db.VarChar(30)
  code        String @unique @db.VarChar(10) // FOB, CIP, LOC, etc.
  description String @db.VarChar(255)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items                    Item[]
  priceStructures         PriceStructure[]
  itemClassificationRanges ItemClassificationRange[]

  @@map("incoterms")
}

// 2. Nature List
model Nature {
  id          String @id @default(cuid()) @db.VarChar(30)
  code        String @unique @db.VarChar(20) // Imported, Local, etc.
  description String @db.VarChar(255)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items           Item[]
  priceStructures PriceStructure[]
  usdRates        UsdRate[]

  @@map("natures")
}

// 3. Status List
model Status {
  id          String @id @default(cuid()) @db.VarChar(30)
  code        String @unique @db.VarChar(30) // Subsidized, Partially Subsidized, Non Subsidized
  description String @db.VarChar(255)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items           Item[]
  priceStructures PriceStructure[]
  usdRates        UsdRate[]

  @@map("statuses")
}

// 4. Item Classification List
model ItemClassification {
  id          String @id @default(cuid()) @db.VarChar(30)
  code        String @unique @db.VarChar(10) // A1, A2, B, C, D, E1, E2
  description String @db.VarChar(255)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items                    Item[]
  priceStructures         PriceStructure[]
  itemClassificationRanges ItemClassificationRange[]
  usdRates                UsdRate[]

  @@map("item_classifications")
}

// 5. Item Classification Range Table
model ItemClassificationRange {
  id                     String @id @default(cuid()) @db.VarChar(30)
  incotermId            String @db.VarChar(30)
  itemClassificationId  String @db.VarChar(30)
  minimumValue          Decimal @db.Decimal(15, 4)
  maximumValue          Decimal @db.Decimal(15, 4)
  isActive              Boolean @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  incoterm            Incoterm           @relation(fields: [incotermId], references: [id])
  itemClassification  ItemClassification @relation(fields: [itemClassificationId], references: [id])

  @@unique([incotermId, itemClassificationId])
  @@map("item_classification_ranges")
}

// 6. Price Structure Table
model PriceStructure {
  id                     String @id @default(cuid()) @db.VarChar(30)
  incotermId            String @db.VarChar(30)
  itemClassificationId  String @db.VarChar(30)
  natureId              String @db.VarChar(30)
  statusId              String @db.VarChar(30)
  freightInsurance      Decimal @db.Decimal(8, 4) // Percentage
  exempted              Decimal @db.Decimal(8, 4) // Percentage
  taxable               Decimal @db.Decimal(8, 4) // Percentage
  distributorMarkup     Decimal @db.Decimal(8, 4) // Percentage
  customerMarkup        Decimal @db.Decimal(8, 4) // Percentage
  isActive              Boolean @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  incoterm            Incoterm           @relation(fields: [incotermId], references: [id])
  itemClassification  ItemClassification @relation(fields: [itemClassificationId], references: [id])
  nature              Nature             @relation(fields: [natureId], references: [id])
  status              Status             @relation(fields: [statusId], references: [id])

  @@unique([incotermId, itemClassificationId, natureId, statusId])
  @@map("price_structures")
}

// 7. Main Coefficient File Table
model Coefficient {
  id                String @id @default(cuid()) @db.VarChar(30)
  coefficientNumber String @unique @db.VarChar(50)
  coefficientDate   DateTime
  fromDate          DateTime
  toDate            DateTime
  isActive          Boolean @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  createdById       String @db.VarChar(30)

  // Relations
  createdBy         User @relation(fields: [createdById], references: [id])
  currencyRates     CurrencyRate[]
  usdRates          UsdRate[]

  @@map("coefficients")
}

// 8. Currency Rate Table (Foreign Currency Rate)
model CurrencyRate {
  id             String @id @default(cuid()) @db.VarChar(30)
  coefficientId  String @db.VarChar(30)
  currency       String @db.VarChar(3) // EUR, JOD, etc.
  rate           Decimal @db.Decimal(12, 6)
  isActive       Boolean @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  coefficient    Coefficient @relation(fields: [coefficientId], references: [id])

  @@unique([coefficientId, currency])
  @@map("currency_rates")
}

// 9. USD Rate per Item Class, Nature, and Status Table
model UsdRate {
  id                     String @id @default(cuid()) @db.VarChar(30)
  coefficientId          String @db.VarChar(30)
  itemClassificationId   String @db.VarChar(30)
  natureId               String @db.VarChar(30)
  statusId               String @db.VarChar(30)
  usdRate                Decimal @db.Decimal(12, 6)
  isActive               Boolean @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  // Relations
  coefficient         Coefficient        @relation(fields: [coefficientId], references: [id])
  itemClassification  ItemClassification @relation(fields: [itemClassificationId], references: [id])
  nature              Nature             @relation(fields: [natureId], references: [id])
  status              Status             @relation(fields: [statusId], references: [id])

  @@unique([coefficientId, itemClassificationId, natureId, statusId])
  @@map("usd_rates")
}

// 10. Item Master File
model Item {
  id                      String @id @default(cuid()) @db.VarChar(30)
  itemCode                String @unique @db.VarChar(50)
  itemName                String @db.VarChar(255)
  description             String? @db.VarChar(1000)

  // Descriptive Flexfield Definition
  incotermId              String @db.VarChar(30)
  poCurrency              String @db.VarChar(3) // Currency code
  poPrice                 Decimal @db.Decimal(15, 4)
  itemClassificationId    String @db.VarChar(30)
  dutyStatus              String @db.VarChar(50)
  natureId                String @db.VarChar(30)
  statusId                String @db.VarChar(30)
  percentageStatus        Decimal? @db.Decimal(8, 4)
  specialPrice            Decimal? @db.Decimal(15, 4)
  percentageSpecialPrice  Decimal? @db.Decimal(8, 4)

  // Additional fields
  isActive                Boolean @default(true)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  createdById             String @db.VarChar(30)

  // Relations
  incoterm                Incoterm           @relation(fields: [incotermId], references: [id])
  itemClassification      ItemClassification @relation(fields: [itemClassificationId], references: [id])
  nature                  Nature             @relation(fields: [natureId], references: [id])
  status                  Status             @relation(fields: [statusId], references: [id])
  createdBy               User               @relation(fields: [createdById], references: [id])

  // Audit trail
  updates                 ItemUpdate[]
  priceListItems          PriceListItem[]

  @@map("items")
}

// Item Update History for audit trail
model ItemUpdate {
  id          String @id @default(cuid()) @db.VarChar(30)
  itemId      String @db.VarChar(30)
  field       String @db.VarChar(100)
  oldValue    String? @db.VarChar(1000)
  newValue    String? @db.VarChar(1000)
  updatedAt   DateTime @default(now())
  updatedById String @db.VarChar(30)

  // Relations
  item        Item @relation(fields: [itemId], references: [id])
  updatedBy   User @relation(fields: [updatedById], references: [id])

  @@map("item_updates")
}

// Price List Management
model PriceList {
  id          String @id @default(cuid()) @db.VarChar(30)
  name        String @db.VarChar(255)
  description String? @db.VarChar(1000)
  version     String @db.VarChar(20)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items       PriceListItem[]

  @@map("price_lists")
}

model PriceListItem {
  id            String @id @default(cuid()) @db.VarChar(30)
  priceListId   String @db.VarChar(30)
  itemId        String @db.VarChar(30)
  publicPrice   Decimal @db.Decimal(15, 4)
  calculatedAt  DateTime @default(now())

  // Relations
  priceList     PriceList @relation(fields: [priceListId], references: [id])
  item          Item      @relation(fields: [itemId], references: [id])

  @@unique([priceListId, itemId])
  @@map("price_list_items")
}
