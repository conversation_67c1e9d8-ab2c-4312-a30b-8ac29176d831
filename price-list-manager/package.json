{"name": "price-list-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx src/seeds/seed.ts", "db:migration:generate": "typeorm-ts-node-commonjs migration:generate -d typeorm.config.ts", "db:migration:run": "typeorm-ts-node-commonjs migration:run -d typeorm.config.ts", "db:migration:revert": "typeorm-ts-node-commonjs migration:revert -d typeorm.config.ts", "db:schema:sync": "typeorm-ts-node-commonjs schema:sync -d typeorm.config.ts", "db:schema:drop": "typeorm-ts-node-commonjs schema:drop -d typeorm.config.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-table": "^8.21.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.523.0", "next": "15.3.4", "oracledb": "^6.8.0", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "reflect-metadata": "^0.2.2", "tailwind-merge": "^3.3.1", "typeorm": "^0.3.25", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/oracledb": "^6.6.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typeorm-ts-node-commonjs": "^0.3.20", "typescript": "^5"}}